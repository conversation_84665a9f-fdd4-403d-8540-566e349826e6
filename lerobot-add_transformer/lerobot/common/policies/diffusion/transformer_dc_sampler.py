"""
SoftREPA-style DiffusionModel_DC for TransformerForDiffusion_DC in robot action diffusion.
Based on SoftREPA's contrastive learning approach for improving action-observation alignment.
Extends the existing DiffusionModel class to add DC token functionality.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Tuple, Dict, Any
import numpy as np
from dataclasses import dataclass

from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.modeling_diffusion import (
    DiffusionModel,
    TransformerForDiffusion_DC,
    _make_noise_scheduler,
)
from lerobot.common.policies.utils import (
    get_device_from_parameters,
    get_dtype_from_parameters
)
from lerobot.common.constants import OBS_ROBOT, OBS_ENV

import pdb


class DiffusionModel_DC(DiffusionModel):
    """
    SoftREPA-style DiffusionModel with DC tokens for contrastive learning.

    Extends the base DiffusionModel to support:
    1. DC token injection in TransformerForDiffusion_DC
    2. Contrastive learning for action-observation alignment
    3. Single-step training with batch contrastive loss
    4. Loading from pretrained DiffusionModel checkpoints
    """

    def __init__(self, config: DiffusionConfig, n_dc_tokens: int = 4, n_dc_layers: int = 6, use_dc_t: bool = True, cond_dim: Optional[int] = None):
        # Store DC configuration
        self.n_dc_tokens = n_dc_tokens
        self.n_dc_layers = n_dc_layers
        self.use_dc_t = use_dc_t
        self.override_cond_dim = cond_dim

        # Create config wrapper with required attributes for base class
        wrapped_config = self._create_config_wrapper(config)

        # Initialize base DiffusionModel first
        super().__init__(wrapped_config)

        # Store original network for weight transfer
        self.original_net = self.net

        # Replace the network with DC version if using transformer
        if config.use_transformer and not (hasattr(config, 'use_dit') and config.use_dit) and not (hasattr(config, 'use_mmdit') and config.use_mmdit):
            # Calculate global condition dimension
            if self.override_cond_dim is not None:
                # Use the provided condition dimension (from pretrained model detection)
                global_cond_dim = self.override_cond_dim
                print(f"Using override condition dimension: {global_cond_dim}")
            else:
                # Calculate from config (same as parent class)
                global_cond_dim = 0
                if hasattr(self.config, 'robot_state_feature') and self.config.robot_state_feature is not None:
                    global_cond_dim = self.config.robot_state_feature.shape[0]

                if hasattr(self.config, 'image_features') and self.config.image_features:
                    num_images = len(self.config.image_features)
                    if hasattr(self, 'rgb_encoder'):
                        if self.config.use_separate_rgb_encoder_per_camera:
                            global_cond_dim += self.rgb_encoder[0].feature_dim * num_images
                        else:
                            global_cond_dim += self.rgb_encoder.feature_dim * num_images

                if hasattr(self.config, 'env_state_feature') and self.config.env_state_feature is not None:
                    global_cond_dim += self.config.env_state_feature.shape[0]

                # If global_cond_dim is still 0, use a default value
                if global_cond_dim == 0:
                    global_cond_dim = 64  # Default condition dimension
                    print(f"Warning: No feature dimensions found, using default global_cond_dim={global_cond_dim}")

            # Create DC version with same architecture (use wrapped config)
            self.net = TransformerForDiffusion_DC(
                config=wrapped_config,
                cond_dim=global_cond_dim,
                n_dc_tokens=n_dc_tokens,
                n_dc_layers=n_dc_layers,
                use_dc_t=use_dc_t,
                use_dc=True
            )

            # Transfer weights from original network to DC version
            self._transfer_pretrained_weights()

        # Initialize DC tokens if they exist
        self.initialize_dc_tokens()

    def _create_config_wrapper(self, config: DiffusionConfig):
        """Create a config wrapper that provides the required attributes for DiffusionModel"""
        from dataclasses import dataclass

        @dataclass
        class FeatureShape:
            shape: list

        class ConfigWrapper:
            """Wrapper that provides backward compatibility for DiffusionModel"""
            def __init__(self, original_config):
                self._original = original_config

                # Set default features
                self.robot_state_feature = FeatureShape(shape=[2])
                self.action_feature = FeatureShape(shape=[2])
                self.image_features = {}
                self.env_state_feature = None

                # Try to extract features from input_features/output_features if available
                if hasattr(original_config, 'input_features') and original_config.input_features:
                    for key, feature in original_config.input_features.items():
                        if 'state' in key.lower():
                            # Handle both dict and object formats
                            shape = feature.get('shape') if isinstance(feature, dict) else feature.shape
                            self.robot_state_feature = FeatureShape(shape=shape)
                        elif 'image' in key.lower():
                            # Handle both dict and object formats
                            shape = feature.get('shape') if isinstance(feature, dict) else feature.shape
                            self.image_features[key] = FeatureShape(shape=shape)

                if hasattr(original_config, 'output_features') and original_config.output_features:
                    for key, feature in original_config.output_features.items():
                        if 'action' in key.lower():
                            # Handle both dict and object formats
                            shape = feature.get('shape') if isinstance(feature, dict) else feature.shape
                            self.action_feature = FeatureShape(shape=shape)

            def __getattr__(self, name):
                # Delegate all other attributes to the original config
                return getattr(self._original, name)

            def __repr__(self):
                return f"ConfigWrapper(image_features={self.image_features}, robot_state_feature={self.robot_state_feature})"

        return ConfigWrapper(config)

    @classmethod
    def from_pretrained(
        cls,
        pretrained_model_path: str,
        config: DiffusionConfig,
        n_dc_tokens: int = 4,
        n_dc_layers: int = 6,
        use_dc_t: bool = True,
        cond_dim: Optional[int] = None
    ):
        """
        Create DiffusionModel_DC from a pretrained lerobot checkpoint.

        Args:
            pretrained_model_path: Path to lerobot checkpoint directory (e.g., checkpoints/195000)
            config: DiffusionConfig for the model
            n_dc_tokens: Number of DC tokens per layer
            n_dc_layers: Number of layers to apply DC tokens
            use_dc_t: Whether to use time-dependent DC tokens

        Returns:
            DiffusionModel_DC with pretrained weights loaded
        """
        from safetensors.torch import load_file
        import json
        from pathlib import Path

        pretrained_path = Path(pretrained_model_path)
        print(f"Loading pretrained model from: {pretrained_path}")

        # Check if it's a lerobot checkpoint directory
        if pretrained_path.is_dir():
            # Look for pretrained_model subdirectory
            model_dir = pretrained_path / "pretrained_model"
            if model_dir.exists():
                safetensors_path = model_dir / "model.safetensors"
                config_path = model_dir / "config.json"
            else:
                # Maybe it's directly the pretrained_model directory
                safetensors_path = pretrained_path / "model.safetensors"
                config_path = pretrained_path / "config.json"
        else:
            # Single file path
            if str(pretrained_path).endswith('.safetensors'):
                safetensors_path = pretrained_path
                config_path = pretrained_path.parent / "config.json"
            else:
                # Assume it's a .pth file
                return cls._load_from_pytorch_checkpoint(pretrained_model_path, config, n_dc_tokens, n_dc_layers, use_dc_t)

        if not safetensors_path.exists():
            raise FileNotFoundError(f"Safetensors file not found: {safetensors_path}")

        print(f"Loading safetensors from: {safetensors_path}")

        # Load pretrained state dict from safetensors
        pretrained_state_dict = load_file(str(safetensors_path))

        # Load config if available
        if config_path.exists():
            with open(config_path, 'r') as f:
                pretrained_config = json.load(f)
            print(f"Loaded pretrained config: {pretrained_config.get('type', 'unknown')} policy")

        # Detect condition dimension from pretrained model if not provided
        if cond_dim is None:
            cond_obs_weight_key = "diffusion.net.cond_obs_emb.weight"
            if cond_obs_weight_key in pretrained_state_dict:
                cond_dim = pretrained_state_dict[cond_obs_weight_key].shape[1]
                print(f"Detected condition dimension from pretrained model: {cond_dim}")
            else:
                raise ValueError("Could not detect condition dimension from pretrained model and cond_dim not provided")

        # Create DC model with correct condition dimension
        model = cls(config, n_dc_tokens, n_dc_layers, use_dc_t, cond_dim=cond_dim)

        # Load pretrained weights (excluding DC tokens)
        model_state_dict = model.state_dict()
        loaded_count = 0
        total_count = 0
        skipped_dc_count = 0

        print("Loading pretrained weights...")
        for name, param in model_state_dict.items():
            total_count += 1

            # Skip DC-specific parameters (they are randomly initialized)
            if 'dc' in name:
                skipped_dc_count += 1
                print(f"  Skipping DC parameter: {name} {param.shape}")
                continue

            # Try to find matching parameter in pretrained model
            # Handle name mapping for different components
            pretrained_name = name
            found = False

            # Handle net.* parameters (transformer weights)
            if name.startswith('net.'):
                # Add 'diffusion.' prefix to match safetensors naming
                pretrained_name = f"diffusion.{name}"
            # Handle rgb_encoder.* parameters
            elif name.startswith('rgb_encoder.'):
                # Add 'diffusion.' prefix to match safetensors naming
                pretrained_name = f"diffusion.{name}"

            # Try the primary mapping
            if pretrained_name in pretrained_state_dict:
                pretrained_param = pretrained_state_dict[pretrained_name]
                if param.shape == pretrained_param.shape:
                    param.data.copy_(pretrained_param.data)
                    loaded_count += 1
                    print(f"  ✓ Loaded: {name} <- {pretrained_name} {param.shape}")
                    found = True
                else:
                    print(f"  ✗ Shape mismatch for {name}: {param.shape} vs {pretrained_param.shape}")

            # Try alternative naming patterns if not found
            if not found:
                alternatives = []

                # For net.* parameters, try without 'net.' prefix
                if name.startswith('net.'):
                    alternatives.append(name[4:])  # Remove 'net.' prefix

                # For rgb_encoder.* parameters, try different variations
                elif name.startswith('rgb_encoder.'):
                    alternatives.append(name)  # Try without diffusion prefix

                # For other parameters, try with 'net.' prefix
                elif not name.startswith('net.') and not name.startswith('rgb_encoder.'):
                    alternatives.append(f"net.{name}")
                    alternatives.append(f"diffusion.{name}")

                # Try each alternative
                for alt_name in alternatives:
                    if alt_name in pretrained_state_dict:
                        pretrained_param = pretrained_state_dict[alt_name]
                        if param.shape == pretrained_param.shape:
                            param.data.copy_(pretrained_param.data)
                            loaded_count += 1
                            print(f"  ✓ Loaded: {name} <- {alt_name} {param.shape}")
                            found = True
                            break

                if not found:
                    print(f"  ✗ Parameter not found in pretrained model: {name} (tried: {pretrained_name}, {alternatives})")

        print(f"Pretrained weights loaded: {loaded_count}/{total_count} parameters")
        print(f"DC parameters skipped: {skipped_dc_count}")
        print(f"Missing parameters: {total_count - loaded_count - skipped_dc_count}")

        return model

    @classmethod
    def _load_from_pytorch_checkpoint(
        cls,
        pretrained_model_path: str,
        config: DiffusionConfig,
        n_dc_tokens: int = 4,
        n_dc_layers: int = 6,
        use_dc_t: bool = True
    ):
        """Fallback method for loading from PyTorch .pth files"""
        print(f"Loading PyTorch checkpoint from: {pretrained_model_path}")

        # Create DC model
        model = cls(config, n_dc_tokens, n_dc_layers, use_dc_t)

        # Load pretrained checkpoint
        checkpoint = torch.load(pretrained_model_path, map_location='cpu')

        # Handle different checkpoint formats
        if 'model_state_dict' in checkpoint:
            pretrained_state_dict = checkpoint['model_state_dict']
        elif 'state_dict' in checkpoint:
            pretrained_state_dict = checkpoint['state_dict']
        else:
            pretrained_state_dict = checkpoint

        # Load pretrained weights (excluding DC tokens)
        model_state_dict = model.state_dict()
        loaded_count = 0
        total_count = 0

        for name, param in model_state_dict.items():
            total_count += 1

            # Skip DC-specific parameters (they are randomly initialized)
            if 'dc' in name:
                continue

            # Load matching parameters from pretrained model
            if name in pretrained_state_dict:
                pretrained_param = pretrained_state_dict[name]

                if param.shape == pretrained_param.shape:
                    param.data.copy_(pretrained_param.data)
                    loaded_count += 1
                else:
                    print(f"  ✗ Shape mismatch for {name}: {param.shape} vs {pretrained_param.shape}")
            else:
                print(f"  ✗ Parameter not found in pretrained model: {name}")

        print(f"Pretrained weights loaded: {loaded_count}/{total_count} parameters")

        return model

    def _transfer_pretrained_weights(self):
        """Transfer weights from original network to DC version"""
        print("Transferring pretrained weights to DC version...")

        # Get state dicts
        original_state_dict = self.original_net.state_dict()
        dc_state_dict = self.net.state_dict()

        # Transfer matching weights
        transferred_count = 0
        total_count = 0

        for name, param in dc_state_dict.items():
            total_count += 1

            # Skip DC-specific parameters
            if 'dc' in name:
                print(f"  Skipping DC parameter: {name}")
                continue

            # Check if parameter exists in original model
            if name in original_state_dict:
                original_param = original_state_dict[name]

                # Check shape compatibility
                if param.shape == original_param.shape:
                    param.data.copy_(original_param.data)
                    transferred_count += 1
                    print(f"  ✓ Transferred: {name} {param.shape}")
                else:
                    print(f"  ✗ Shape mismatch: {name} {param.shape} vs {original_param.shape}")
            else:
                print(f"  ✗ Not found in original: {name}")

        print(f"Weight transfer complete: {transferred_count}/{total_count} parameters transferred")

        # Clean up original network to save memory
        del self.original_net

    def initialize_dc_tokens(self):
        """Initialize DC tokens with small random values"""
        if hasattr(self.net, 'dc_tokens'):
            nn.init.normal_(self.net.dc_tokens, mean=0, std=0.02)
            if hasattr(self.net, 'dc_t_tokens') and self.net.dc_t_tokens is not None:
                nn.init.normal_(self.net.dc_t_tokens.weight, mean=0, std=0.02)

    def freeze_base_model(self):
        """Freeze all parameters except DC tokens (for SoftREPA training)"""
        for name, param in self.named_parameters():
            if 'dc' in name:
                param.requires_grad = True
            else:
                param.requires_grad = False

    def get_dc_parameters(self) -> List[torch.nn.Parameter]:
        """Get only DC-related parameters for optimization"""
        dc_params = []
        for name, param in self.named_parameters():
            if 'dc' in name and param.requires_grad:
                dc_params.append(param)
        return dc_params

    def print_parameter_stats(self):
        """Print detailed parameter statistics"""
        total_params = 0
        trainable_params = 0
        dc_params = 0

        print("Parameter Statistics:")
        print("-" * 50)

        for name, param in self.named_parameters():
            param_count = param.numel()
            total_params += param_count

            if param.requires_grad:
                trainable_params += param_count

            if 'dc' in name:
                dc_params += param_count
                print(f"DC Parameter: {name}")
                print(f"  Shape: {param.shape}")
                print(f"  Count: {param_count:,}")
                print(f"  Trainable: {param.requires_grad}")

        print("-" * 50)
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        print(f"DC parameters: {dc_params:,}")
        print(f"Frozen parameters: {total_params - trainable_params:,}")
        print(f"Training efficiency: {trainable_params/total_params*100:.2f}% of total params")

    def compute_contrastive_error(self, batch: dict[str, torch.Tensor], use_dc: bool = True) -> torch.Tensor:
        """
        Compute contrastive error matrix for SoftREPA training.

        Args:
            batch: Dictionary containing 'action' and observation data
            use_dc: Whether to use DC tokens

        Returns:
            (B, B) error matrix where error[i,j] is the prediction error
            between action_i and observation_j
        """
        # Preprocess batch to add observation.images if needed
        if self.config.image_features:
            batch = dict(batch)  # shallow copy so that adding a key doesn't modify the original
            batch["observation.images"] = torch.stack(
                [batch[key] for key in self.config.image_features], dim=-4
            )

        # Prepare global conditioning (reuse parent method)
        global_cond = self._prepare_global_conditioning(batch)  # (B, global_cond_dim)
        print(f"Global conditioning shape: {global_cond.shape}")
        print(f"Expected condition dimension: {self.override_cond_dim if self.override_cond_dim else 'auto'}")

        # Get actions and create noise
        trajectory = batch["action"]  # (B, action_dim) for single-step actions

        # Handle single-step actions vs trajectory sequences
        if len(trajectory.shape) == 2:
            # Single-step actions: [B, action_dim] -> [B, 1, action_dim]
            trajectory = trajectory.unsqueeze(1)

        batch_size = trajectory.shape[0]

        # Sample noise and timesteps
        eps = torch.randn(trajectory.shape, device=trajectory.device)
        timesteps = torch.randint(
            low=0,
            high=self.noise_scheduler.config.num_train_timesteps,
            size=(trajectory.shape[0],),
            device=trajectory.device,
        ).long()

        # Add noise to trajectories
        noisy_trajectory = self.noise_scheduler.add_noise(trajectory, eps, timesteps)

        # Expand for contrastive learning: each action with all observations
        # Actions: (B, horizon, action_dim) -> (B*B, horizon, action_dim)
        expanded_actions = trajectory.unsqueeze(1).expand(-1, batch_size, -1, -1).reshape(
            batch_size * batch_size, *trajectory.shape[1:]
        )
        expanded_noisy_actions = noisy_trajectory.unsqueeze(1).expand(-1, batch_size, -1, -1).reshape(
            batch_size * batch_size, *noisy_trajectory.shape[1:]
        )
        expanded_eps = eps.unsqueeze(1).expand(-1, batch_size, -1, -1).reshape(
            batch_size * batch_size, *eps.shape[1:]
        )

        # Observations: (B, global_cond_dim) -> (B*B, global_cond_dim)
        # Each observation should be paired with all actions
        expanded_global_cond = global_cond.unsqueeze(1).expand(-1, batch_size, -1).reshape(
            batch_size * batch_size, -1
        )

        # Timesteps: (B,) -> (B*B,)
        expanded_timesteps = timesteps.unsqueeze(1).expand(-1, batch_size).reshape(-1)

        # Set DC usage
        if hasattr(self.net, 'use_dc'):
            self.net.use_dc = use_dc

        # Forward pass through network
        pred = self.net(expanded_noisy_actions, expanded_timesteps, global_cond=expanded_global_cond)

        # Compute prediction errors
        if self.config.prediction_type == "epsilon":
            target = expanded_eps
        elif self.config.prediction_type == "sample":
            target = expanded_actions
        else:
            raise ValueError(f"Unsupported prediction type {self.config.prediction_type}")

        # Calculate MSE error for each action-observation pair
        errors = F.mse_loss(pred, target, reduction='none').mean(dim=(1, 2))  # (B*B,)

        # Reshape to contrastive matrix
        error_matrix = errors.reshape(batch_size, batch_size)  # (B, B)

        return error_matrix


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss for action-observation alignment.
    Adapted from SoftREPA for robot action sequences.
    """
    
    def __init__(self, temp: float = 0.07, scale: float = 4.0, dweight: float = 0.0, device: str = 'cuda'):
        super().__init__()
        self.device = device
        self.temp = nn.Parameter(torch.tensor(temp, device=device))
        self.scale = nn.Parameter(torch.tensor(scale, device=device))
        self.dweight = dweight  # diffusion loss weight
    
    def get_mask(self, shape: Tuple[int, int]) -> torch.Tensor:
        """Create positive sample mask (diagonal)"""
        mask = torch.zeros(shape, device=self.device)
        n_b, _ = shape
        index = torch.arange(n_b, device=self.device)
        mask[index, index] = 1
        return mask
    
    def forward(self, errors: torch.Tensor) -> torch.Tensor:
        """
        Compute contrastive loss from prediction errors.

        Args:
            errors: (B, B) error matrix where errors[i,j] is the error
                   between action_i and condition_j

        Returns:
            Scalar contrastive loss
        """
        batch_size = errors.shape[0]
    
        # Convert errors to similarity scores (lower error = higher similarity)
        # Following SoftREPA: logits = scale * exp(-errors/temp)
        logits = self.scale * torch.exp(-errors / self.temp)  # (B, B)

        # Create target labels (diagonal elements are positive pairs)
        targets = torch.arange(batch_size, device=errors.device)  # [0, 1, 2, ..., B-1]

        # Cross-entropy loss for contrastive learning
        # Each row should predict its corresponding diagonal element
        loss = F.cross_entropy(logits, targets)

        # Optional: add diffusion loss weight for diagonal elements
        if self.dweight > 0:
            diagonal_errors = torch.diag(errors)
            loss += self.dweight * diagonal_errors.mean()

        return loss


class SoftREPATrainer(nn.Module):
    """
    SoftREPA-style trainer for robot action diffusion.
    Implements contrastive learning for action-observation alignment.
    """

    def __init__(self, diffusion_model: DiffusionModel_DC):
        super().__init__()
        self.diffusion_model = diffusion_model
        self.device = get_device_from_parameters(diffusion_model)
        self.dtype = get_dtype_from_parameters(diffusion_model)

    def forward(
        self,
        batch: dict[str, torch.Tensor],
        use_dc: bool = True
    ) -> torch.Tensor:
        """
        Forward pass for contrastive learning.

        Args:
            batch: Dictionary containing 'action' and observation data
            use_dc: whether to use DC tokens

        Returns:
            (B, B) error matrix for contrastive learning
        """
        # Use the DiffusionModel_DC's contrastive error computation
        error_matrix = self.diffusion_model.compute_contrastive_error(batch, use_dc=use_dc)

        return error_matrix


# 注意：我们直接使用DiffusionModel中现有的noise_scheduler，
# 不需要重新实现DDPMScheduler，因为DiffusionModel已经有了完整的调度器


class TransformerDCInference:
    """
    Inference engine for DiffusionModel_DC with DC tokens.
    Supports both standard sampling and DC-enhanced sampling.
    """

    def __init__(self, diffusion_model: DiffusionModel_DC, num_inference_steps: int = 50):
        self.diffusion_model = diffusion_model
        self.num_inference_steps = num_inference_steps
        self.device = get_device_from_parameters(diffusion_model)

        # Use the existing noise scheduler from DiffusionModel
        self.noise_scheduler = diffusion_model.noise_scheduler

        # Create inference timesteps
        self.timesteps = torch.linspace(
            diffusion_model.noise_scheduler.config.num_train_timesteps - 1, 0,
            num_inference_steps, dtype=torch.long
        )

    def sample(
        self,
        batch: dict[str, torch.Tensor],
        use_dc: bool = True,
        generator: Optional[torch.Generator] = None
    ) -> torch.Tensor:
        """
        Generate action sequences using DDPM sampling.

        Args:
            batch: Dictionary containing observation data (same format as training)
            use_dc: whether to use DC tokens during sampling
            generator: random number generator for reproducibility

        Returns:
            (B, T, action_dim) generated action sequences
        """
        # Prepare global conditioning using existing method
        global_cond = self.diffusion_model._prepare_global_conditioning(batch)
        batch_size = global_cond.shape[0]

        # Initialize with random noise
        actions = torch.randn(
            batch_size,
            self.diffusion_model.config.horizon,
            self.diffusion_model.config.action_feature.shape[0],
            device=self.device,
            dtype=get_dtype_from_parameters(self.diffusion_model),
            generator=generator
        )

        # Set DC usage
        if hasattr(self.diffusion_model.net, 'use_dc'):
            self.diffusion_model.net.use_dc = use_dc

        # Denoising loop
        for t in self.timesteps:
            timestep_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            # Predict noise
            with torch.no_grad():
                noise_pred = self.diffusion_model.net(
                    actions, timestep_batch, global_cond=global_cond
                )

            # Update sample using noise scheduler
            actions = self.noise_scheduler.step(noise_pred, t.item(), actions, generator=generator).prev_sample

        return actions

    def sample_with_guidance(
        self,
        batch: dict[str, torch.Tensor],
        guidance_scale: float = 1.0,
        use_dc: bool = True,
        generator: Optional[torch.Generator] = None
    ) -> torch.Tensor:
        """
        Generate action sequences with classifier-free guidance.

        Args:
            batch: Dictionary containing observation data
            guidance_scale: strength of guidance (1.0 = no guidance)
            use_dc: whether to use DC tokens
            generator: random number generator

        Returns:
            (B, T, action_dim) generated action sequences
        """
        if guidance_scale == 1.0:
            return self.sample(batch, use_dc, generator)

        # Prepare global conditioning
        global_cond = self.diffusion_model._prepare_global_conditioning(batch)
        batch_size = global_cond.shape[0]

        # Initialize with random noise
        actions = torch.randn(
            batch_size,
            self.diffusion_model.config.horizon,
            self.diffusion_model.config.action_feature.shape[0],
            device=self.device,
            dtype=get_dtype_from_parameters(self.diffusion_model),
            generator=generator
        )

        # Create unconditional conditions (zeros)
        uncond_global_cond = torch.zeros_like(global_cond)

        # Set DC usage
        if hasattr(self.diffusion_model.net, 'use_dc'):
            self.diffusion_model.net.use_dc = use_dc

        # Denoising loop with guidance
        for t in self.timesteps:
            timestep_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            with torch.no_grad():
                # Conditional prediction
                cond_noise_pred = self.diffusion_model.net(
                    actions, timestep_batch, global_cond=global_cond
                )

                # Unconditional prediction
                uncond_noise_pred = self.diffusion_model.net(
                    actions, timestep_batch, global_cond=uncond_global_cond
                )

                # Apply classifier-free guidance
                noise_pred = uncond_noise_pred + guidance_scale * (cond_noise_pred - uncond_noise_pred)

            # Update sample
            actions = self.noise_scheduler.step(noise_pred, t.item(), actions, generator=generator).prev_sample

        return actions


def create_diffusion_model_dc(
    config: DiffusionConfig,
    n_dc_tokens: int = 4,
    n_dc_layers: int = 6,
    use_dc_t: bool = True,
    pretrained_model_path: Optional[str] = None
) -> DiffusionModel_DC:
    """
    Factory function to create a DiffusionModel_DC.

    Args:
        config: DiffusionConfig for the model
        n_dc_tokens: number of DC tokens per layer
        n_dc_layers: number of layers to apply DC tokens
        use_dc_t: whether to use time-dependent DC tokens
        pretrained_model_path: path to pretrained DiffusionModel checkpoint (optional)

    Returns:
        Configured DiffusionModel_DC
    """
    if pretrained_model_path is not None:
        return DiffusionModel_DC.from_pretrained(
            pretrained_model_path=pretrained_model_path,
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=use_dc_t
        )
    else:
        return DiffusionModel_DC(
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=use_dc_t
        )
